"""二进制文件验证器 - Binary File Validation System
=================================================

提供编译后二进制文件的全面验证功能，确保二进制文件符合LibAFL兼容性要求。

功能特性：
- 二进制文件存在性和完整性验证
- LibAFL兼容性检查（符号表、入口点）
- 文件格式和架构验证
- 依赖库检查
- 安全性验证（ASLR、stack canaries等）
- 性能特征分析
"""

from __future__ import annotations

import logging
import os
import shutil
import subprocess
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any

logger = logging.getLogger(__name__)


@dataclass
class BinaryValidationResult:
    """二进制文件验证结果"""

    is_valid: bool
    binary_path: str
    file_size: int = 0
    architecture: str = ""
    format_type: str = ""
    has_symbols: bool = False
    has_libafl_entry: bool = False
    has_asan: bool = False
    has_coverage: bool = False
    dependencies: list[str] = field(default_factory=list)
    security_features: dict[str, bool] = field(default_factory=dict)
    error_messages: list[str] = field(default_factory=list)
    warnings: list[str] = field(default_factory=list)
    metadata: dict[str, Any] = field(default_factory=dict)

    @property
    def is_libafl_compatible(self) -> bool:
        """检查是否LibAFL兼容"""
        return self.is_valid and self.has_libafl_entry and len(self.error_messages) == 0

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "is_valid": self.is_valid,
            "is_libafl_compatible": self.is_libafl_compatible,
            "binary_path": self.binary_path,
            "file_size": self.file_size,
            "architecture": self.architecture,
            "format_type": self.format_type,
            "has_symbols": self.has_symbols,
            "has_libafl_entry": self.has_libafl_entry,
            "has_asan": self.has_asan,
            "has_coverage": self.has_coverage,
            "dependencies": self.dependencies,
            "security_features": self.security_features,
            "error_messages": self.error_messages,
            "warnings": self.warnings,
            "metadata": self.metadata,
        }


class BinaryValidator:
    """二进制文件验证器

    提供全面的二进制文件验证功能，确保编译后的二进制文件符合
    LibAFL兼容性要求和安全标准。
    """

    def __init__(self) -> None:
        """初始化二进制验证器"""
        self.required_tools = ["file", "objdump", "nm", "ldd"]
        self.optional_tools = ["readelf", "strings"]
        self._check_system_tools()

    def _check_system_tools(self) -> None:
        """检查系统工具可用性"""
        missing_tools = []
        for tool in self.required_tools:
            if not shutil.which(tool):
                missing_tools.append(tool)

        if missing_tools:
            logger.warning(f"Missing required tools: {missing_tools}")
            logger.warning("Some validation features may be limited")
        else:
            logger.debug("All required validation tools available")

    async def validate_binary(
        self,
        binary_path: str,
        *,
        check_symbols: bool = True,
        check_dependencies: bool = True,
        check_security: bool = True,
        timeout: float = 30.0,
    ) -> BinaryValidationResult:
        """验证二进制文件

        Args:
            binary_path: 二进制文件路径
            check_symbols: 是否检查符号表
            check_dependencies: 是否检查依赖
            check_security: 是否检查安全特性
            timeout: 验证超时时间

        Returns:
            BinaryValidationResult: 验证结果
        """
        result = BinaryValidationResult(is_valid=False, binary_path=binary_path)

        try:
            # 1. 基本文件验证
            await self._validate_file_existence(result)
            if not result.is_valid:
                return result

            # 2. 文件格式和架构检查
            await self._check_file_format(result, timeout)

            # 3. 符号表检查
            if check_symbols:
                await self._check_symbols(result, timeout)

            # 4. LibAFL兼容性检查
            await self._check_libafl_compatibility(result, timeout)

            # 5. 依赖库检查
            if check_dependencies:
                await self._check_dependencies(result, timeout)

            # 6. 安全特性检查
            if check_security:
                await self._check_security_features(result, timeout)

            # 7. 最终验证评估
            self._evaluate_final_validation(result)

            logger.info(f"Binary validation completed: {result.binary_path}")
            logger.info(
                f"Valid: {result.is_valid}, LibAFL compatible: {result.is_libafl_compatible}"
            )

        except Exception as e:
            result.is_valid = False
            result.error_messages.append(f"Validation failed: {e}")
            logger.error(f"Binary validation error: {e}")

        return result

    async def _validate_file_existence(self, result: BinaryValidationResult) -> None:
        """验证文件存在性和基本属性"""
        path = Path(result.binary_path)

        if not path.exists():
            result.error_messages.append(f"Binary file not found: {result.binary_path}")
            return

        if not path.is_file():
            result.error_messages.append(f"Path is not a file: {result.binary_path}")
            return

        # 获取文件大小
        try:
            result.file_size = path.stat().st_size
            if result.file_size == 0:
                result.error_messages.append("Binary file is empty")
                return
            elif result.file_size < 1024:  # 小于1KB可能有问题
                result.warnings.append(
                    f"Binary file is very small: {result.file_size} bytes"
                )
        except OSError as e:
            result.error_messages.append(f"Cannot access file attributes: {e}")
            return

        # 检查文件权限
        if not os.access(result.binary_path, os.R_OK):
            result.error_messages.append("Binary file is not readable")
            return

        if not os.access(result.binary_path, os.X_OK):
            result.warnings.append("Binary file is not executable")

        result.is_valid = True
        result.metadata["file_exists"] = True
        result.metadata["file_size_mb"] = result.file_size / (1024 * 1024)

    async def _check_file_format(
        self, result: BinaryValidationResult, timeout: float
    ) -> None:
        """检查文件格式和架构"""
        if not shutil.which("file"):
            result.warnings.append(
                "'file' command not available, skipping format check"
            )
            return

        try:
            process = await asyncio.create_subprocess_exec(
                "file",
                "-b",
                result.binary_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            if process.returncode != 0:
                result.warnings.append(f"File format check failed: {stderr.decode()}")
                return

            file_info = stdout.decode().strip().lower()
            result.metadata["file_info"] = file_info

            # 检查是否为ELF文件（Linux）
            if "elf" in file_info:
                result.format_type = "ELF"

                # 提取架构信息
                if "x86-64" in file_info or "x86_64" in file_info:
                    result.architecture = "x86_64"
                elif "i386" in file_info or "x86" in file_info:
                    result.architecture = "i386"
                elif "aarch64" in file_info or "arm64" in file_info:
                    result.architecture = "aarch64"
                elif "arm" in file_info:
                    result.architecture = "arm"
                else:
                    result.architecture = "unknown"

                # 检查是否为可执行文件
                if "executable" not in file_info:
                    result.warnings.append("File is not marked as executable")

            else:
                result.warnings.append(f"Unexpected file format: {file_info}")
                result.format_type = "unknown"

        except asyncio.TimeoutError:
            result.warnings.append("File format check timed out")
        except Exception as e:
            result.warnings.append(f"File format check failed: {e}")

    async def _check_symbols(
        self, result: BinaryValidationResult, timeout: float
    ) -> None:
        """检查符号表"""
        if not shutil.which("nm"):
            result.warnings.append("'nm' command not available, skipping symbol check")
            return

        try:
            process = await asyncio.create_subprocess_exec(
                "nm",
                "-D",
                result.binary_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            if process.returncode == 0:
                symbols = stdout.decode()
                result.has_symbols = len(symbols.strip()) > 0
                result.metadata["symbol_count"] = len(symbols.splitlines())
            else:
                # 尝试静态符号
                process = await asyncio.create_subprocess_exec(
                    "nm",
                    result.binary_path,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                )
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=timeout
                )

                if process.returncode == 0:
                    symbols = stdout.decode()
                    result.has_symbols = len(symbols.strip()) > 0
                    result.metadata["symbol_count"] = len(symbols.splitlines())
                else:
                    result.warnings.append("No symbols found (stripped binary)")

        except asyncio.TimeoutError:
            result.warnings.append("Symbol check timed out")
        except Exception as e:
            result.warnings.append(f"Symbol check failed: {e}")

    async def _check_libafl_compatibility(
        self, result: BinaryValidationResult, timeout: float
    ) -> None:
        """检查LibAFL兼容性"""
        # 检查必要的函数入口点
        libafl_symbols = [
            "LLVMFuzzerTestOneInput",
            "LLVMFuzzerInitialize",
            "__libafl_main",
        ]

        result.has_libafl_entry = await self._check_symbols_present(
            result.binary_path, libafl_symbols, timeout
        )

        if not result.has_libafl_entry:
            result.error_messages.append(
                "Binary missing LibAFL entry points (LLVMFuzzerTestOneInput, etc.)"
            )
        else:
            result.metadata["libafl_entry_found"] = True

        # 检查AddressSanitizer
        asan_symbols = ["__asan_init", "__sanitizer_", "AddressSanitizer"]
        result.has_asan = await self._check_symbols_present(
            result.binary_path, asan_symbols, timeout
        )

        # 检查覆盖率插桩
        coverage_symbols = ["__llvm_gcov", "__gcov_", "__sancov_", "llvm.x86.sancov"]
        result.has_coverage = await self._check_symbols_present(
            result.binary_path, coverage_symbols, timeout
        )

    async def _check_symbols_present(
        self, binary_path: str, symbols: list[str], timeout: float
    ) -> bool:
        """检查指定符号是否存在"""
        if not shutil.which("strings"):
            return False

        try:
            process = await asyncio.create_subprocess_exec(
                "strings",
                binary_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            if process.returncode != 0:
                return False

            strings_output = stdout.decode().lower()

            # 检查是否包含任一符号
            for symbol in symbols:
                if symbol.lower() in strings_output:
                    return True

            return False

        except (asyncio.TimeoutError, Exception):
            return False

    async def _check_dependencies(
        self, result: BinaryValidationResult, timeout: float
    ) -> None:
        """检查动态库依赖"""
        if not shutil.which("ldd"):
            result.warnings.append(
                "'ldd' command not available, skipping dependency check"
            )
            return

        try:
            process = await asyncio.create_subprocess_exec(
                "ldd",
                result.binary_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            if process.returncode == 0:
                dependencies = []
                for line in stdout.decode().splitlines():
                    line = line.strip()
                    if "=>" in line:
                        lib_name = line.split("=>")[0].strip()
                        dependencies.append(lib_name)
                    elif line.startswith("/"):
                        # 直接路径格式
                        lib_name = Path(line.split()[0]).name
                        dependencies.append(lib_name)

                result.dependencies = dependencies
                result.metadata["dependency_count"] = len(dependencies)

                # 检查关键依赖
                critical_deps = ["libc.so", "libstdc++.so", "libgcc_s.so"]
                missing_critical = [
                    dep
                    for dep in critical_deps
                    if not any(dep in d for d in dependencies)
                ]

                if missing_critical:
                    result.warnings.append(
                        f"Missing critical dependencies: {missing_critical}"
                    )

            else:
                result.warnings.append("Failed to analyze dependencies")

        except asyncio.TimeoutError:
            result.warnings.append("Dependency check timed out")
        except Exception as e:
            result.warnings.append(f"Dependency check failed: {e}")

    async def _check_security_features(
        self, result: BinaryValidationResult, timeout: float
    ) -> None:
        """检查安全特性"""
        security_features = {}

        # 使用readelf检查安全特性
        if shutil.which("readelf"):
            try:
                # 检查栈保护
                security_features["stack_canary"] = await self._check_stack_canary(
                    result.binary_path, timeout
                )

                # 检查ASLR支持（PIE）
                security_features["pie_enabled"] = await self._check_pie_enabled(
                    result.binary_path, timeout
                )

                # 检查NX bit（No-Execute）
                security_features["nx_enabled"] = await self._check_nx_enabled(
                    result.binary_path, timeout
                )

            except Exception as e:
                result.warnings.append(f"Security feature check failed: {e}")

        result.security_features = security_features
        result.metadata["security_check_completed"] = True

    async def _check_stack_canary(self, binary_path: str, timeout: float) -> bool:
        """检查栈保护"""
        return await self._check_symbols_present(
            binary_path, ["__stack_chk_fail", "__stack_chk_guard"], timeout
        )

    async def _check_pie_enabled(self, binary_path: str, timeout: float) -> bool:
        """检查PIE（位置无关可执行文件）"""
        try:
            process = await asyncio.create_subprocess_exec(
                "readelf",
                "-h",
                binary_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            if process.returncode == 0:
                output = stdout.decode()
                return "DYN (Shared object file)" in output

            return False

        except Exception:
            return False

    async def _check_nx_enabled(self, binary_path: str, timeout: float) -> bool:
        """检查NX bit"""
        try:
            process = await asyncio.create_subprocess_exec(
                "readelf",
                "-l",
                binary_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            if process.returncode == 0:
                output = stdout.decode()
                # 检查GNU_STACK段的权限
                for line in output.splitlines():
                    if "GNU_STACK" in line and "RWE" not in line:
                        return True

            return False

        except Exception:
            return False

    def _evaluate_final_validation(self, result: BinaryValidationResult) -> None:
        """评估最终验证结果"""
        # 基本验证通过才能标记为有效
        if not result.is_valid:
            return

        # 检查关键错误
        if result.error_messages:
            result.is_valid = False
            return

        # 警告不影响基本有效性，但会影响LibAFL兼容性评分
        compatibility_score = 100

        if not result.has_libafl_entry:
            compatibility_score -= 50
            result.error_messages.append("Missing LibAFL entry points")

        if not result.has_asan:
            compatibility_score -= 20
            result.warnings.append("AddressSanitizer not detected")

        if not result.has_coverage:
            compatibility_score -= 20
            result.warnings.append("Coverage instrumentation not detected")

        if result.file_size > 100 * 1024 * 1024:  # >100MB
            compatibility_score -= 10
            result.warnings.append("Binary file is very large")

        result.metadata["compatibility_score"] = compatibility_score
        result.metadata["final_evaluation_completed"] = True

        # 如果兼容性评分过低，标记为无效
        if compatibility_score < 50:
            result.is_valid = False
            result.error_messages.append(
                f"Compatibility score too low: {compatibility_score}"
            )


# 导入asyncio（延迟导入避免循环依赖）
import asyncio


async def validate_compiled_binary(
    binary_path: str,
    *,
    comprehensive: bool = True,
    timeout: float = 30.0,
) -> BinaryValidationResult:
    """验证编译后的二进制文件

    便利函数，用于快速验证二进制文件。

    Args:
        binary_path: 二进制文件路径
        comprehensive: 是否进行全面检查
        timeout: 验证超时时间

    Returns:
        BinaryValidationResult: 验证结果
    """
    validator = BinaryValidator()

    return await validator.validate_binary(
        binary_path,
        check_symbols=comprehensive,
        check_dependencies=comprehensive,
        check_security=comprehensive,
        timeout=timeout,
    )


def create_validation_summary(result: BinaryValidationResult) -> str:
    """创建验证结果摘要

    Args:
        result: 验证结果

    Returns:
        str: 格式化的摘要字符串
    """
    lines = []
    lines.append(f"📁 Binary: {result.binary_path}")
    lines.append(f"✅ Valid: {result.is_valid}")
    lines.append(f"🔧 LibAFL Compatible: {result.is_libafl_compatible}")

    if result.file_size > 0:
        size_mb = result.file_size / (1024 * 1024)
        lines.append(f"📏 Size: {size_mb:.2f} MB")

    if result.architecture:
        lines.append(f"🏗️ Architecture: {result.architecture}")

    if result.format_type:
        lines.append(f"📋 Format: {result.format_type}")

    if result.has_asan:
        lines.append("🛡️ AddressSanitizer: ✅")

    if result.has_coverage:
        lines.append("📊 Coverage: ✅")

    if result.dependencies:
        lines.append(f"📚 Dependencies: {len(result.dependencies)}")

    if result.error_messages:
        lines.append("❌ Errors:")
        for error in result.error_messages:
            lines.append(f"  • {error}")

    if result.warnings:
        lines.append("⚠️ Warnings:")
        for warning in result.warnings:
            lines.append(f"  • {warning}")

    return "\n".join(lines)
