"""Telemetry Stream - 基于protobuf的共享内存遥测数据读取器
========================================================

基于共享内存的高性能遥测数据流读取器, 用于从Rust fuzzing engine
读取实时遥测数据。现在使用protobuf格式以实现更好的跨语言兼容性。
"""

from __future__ import annotations

import asyncio
import logging
import mmap
import struct
from dataclasses import dataclass
from pathlib import Path
from typing import Any

from .telemetry_pb2 import (  # type: ignore[attr-defined]
    TelemetryDataType,
    TelemetryEntry,
)

logger = logging.getLogger(__name__)

# 显式导出protobuf类型以满足mypy检查
__all__ = [
    "TelemetryDataPlane",
    "TelemetryDataType",
    "TelemetryEntry",
    "TelemetryReader",
    "create_telemetry_reader",
]


@dataclass
class TelemetryEntryDict:
    """遥测数据条目字典包装器"""

    type: str
    instance_id: str
    timestamp: float
    data: dict[str, Any]

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "type": self.type,
            "instance_id": self.instance_id,
            "timestamp": self.timestamp,
            **self.data,
        }


class TelemetryDataPlane:
    """基于protobuf的共享内存遥测数据平面

    用于高性能地从Rust fuzzing engine读取遥测数据。
    使用内存映射文件实现低延迟的数据传输。
    """

    # 共享内存文件格式常量 - 与Rust实现保持一致
    MAGIC_NUMBER = 0x54454C45  # 'TELE' - 格式标识
    VERSION = 2  # 版本2表示protobuf格式
    METADATA_SIZE = 64  # 元数据区域大小(与Rust一致)
    ENTRY_HEADER_SIZE = 4  # 条目长度字段大小

    def __init__(self, stream_name: str = "fuzzlm_telemetry"):
        """初始化遥测数据平面

        Args:
            stream_name: 共享内存流名称

        """
        self.stream_name = stream_name

        # 与Rust端保持一致的路径处理 - 修复路径同步问题
        import os

        base_path = os.environ.get("FUZZLM_SHM_DIR")
        if base_path is None:
            # 首先尝试找到实际的共享内存文件位置
            possible_paths = self._get_possible_telemetry_paths(stream_name)

            # 如果找到了现有文件，使用其目录
            for path in possible_paths:
                if os.path.exists(path):
                    base_path = os.path.dirname(path)
                    logger.info(
                        f"Found existing telemetry file, using path: {base_path}"
                    )
                    break

            if base_path is None:
                # 使用项目根目录下的runtime/temp
                project_root = os.environ.get("FUZZLM_PROJECT_ROOT")
                if project_root:
                    base_path = os.path.join(project_root, "runtime", "temp")
                else:
                    # 尝试找到项目根目录，与Rust端逻辑保持一致
                    current_dir = os.getcwd()

                    # 优先检查fuzzing-engine目录（Rust进程的工作目录）
                    if "fuzzing-engine" in current_dir:
                        # 如果在fuzzing-engine目录，使用该目录下的runtime/temp
                        base_path = os.path.join(current_dir, "runtime", "temp")
                    elif "fuzzlm-agent" in current_dir:
                        # 找到包含fuzzlm-agent的部分
                        parts = current_dir.split(os.sep)
                        found_root = None
                        for i, part in enumerate(parts):
                            if part == "fuzzlm-agent":
                                found_root = os.sep.join(parts[: i + 1])
                                break
                        if found_root:
                            # 尝试fuzzing-engine路径优先
                            fuzzing_engine_path = os.path.join(
                                found_root,
                                "fuzzlm_agent",
                                "fuzzing-engine",
                                "runtime",
                                "temp",
                            )
                            if os.path.exists(fuzzing_engine_path):
                                base_path = fuzzing_engine_path
                            else:
                                base_path = os.path.join(found_root, "runtime", "temp")
                        else:
                            base_path = os.path.join(current_dir, "runtime", "temp")
                    else:
                        # 默认使用当前目录下的runtime/temp
                        base_path = os.path.join(current_dir, "runtime", "temp")

        self.base_path = base_path
        self.shm_path = Path(f"{base_path}/{stream_name}")
        self.mmap_obj: mmap.mmap | None = None
        self.file_handle: Any | None = None
        self.read_position = 0
        self.connected = False

        logger.info(
            f"Telemetry data plane initialized for stream: {stream_name} at {self.shm_path} (protobuf)"
        )

    def connect(self) -> bool:
        """连接到共享内存，支持路径查找和重试机制

        Returns:
            是否成功连接

        """
        if self.connected:
            return True

        # 尝试多个可能的路径
        possible_paths = self._get_possible_telemetry_paths(self.stream_name)

        for attempt_path in possible_paths:
            logger.debug(f"Trying telemetry path: {attempt_path}")

            if Path(attempt_path).exists():
                logger.info(f"Found telemetry file at: {attempt_path}")
                self.shm_path = Path(attempt_path)
                break
        else:
            # 如果所有路径都失败，记录详细错误信息
            logger.error(
                "❌ Shared memory file not found in any of the following locations:"
            )
            for path in possible_paths:
                logger.error(f"  - {path}")
            logger.error(
                "This indicates Python and Rust are using different file names or paths."
            )
            logger.error(
                "Make sure fuzzer is started before initializing TelemetryReader."
            )
            return False

        try:

            # 打开共享内存文件
            self.file_handle = open(self.shm_path, "r+b")

            # 创建内存映射
            file_size = self.shm_path.stat().st_size
            if file_size == 0:
                # 如果文件为空, 扩展到最小大小
                min_size = self.METADATA_SIZE + (1024 * 1024)  # 1MB数据区域
                self.file_handle.truncate(min_size)
                file_size = min_size

            self.mmap_obj = mmap.mmap(
                self.file_handle.fileno(),
                file_size,
                access=mmap.ACCESS_READ,
            )

            # 验证魔数和版本
            magic = struct.unpack("<I", self.mmap_obj[:4])[0]
            version = struct.unpack("<I", self.mmap_obj[4:8])[0]

            if magic != self.MAGIC_NUMBER:
                logger.warning(
                    f"Invalid magic number: {magic:#x}, expected: {self.MAGIC_NUMBER:#x}"
                )
                # 对于测试环境, 允许继续

            if version != self.VERSION:
                logger.warning(f"Version mismatch: {version}, expected: {self.VERSION}")

            self.connected = True
            logger.info(
                f"Connected to shared memory: {self.shm_path} (protobuf v{version})"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to connect to shared memory: {e}")
            self.disconnect()
            return False

    def disconnect(self) -> None:
        """断开共享内存连接"""
        if self.mmap_obj:
            self.mmap_obj.close()
            self.mmap_obj = None

        if self.file_handle:
            self.file_handle.close()
            self.file_handle = None

        self.connected = False
        logger.info("Disconnected from shared memory")

    async def read_entry(self, timeout: float = 0.01) -> dict[str, Any] | None:
        """读取单个遥测条目(非阻塞)

        Args:
            timeout: 超时时间(秒)

        Returns:
            遥测条目字典, 如果没有数据返回None

        """
        if not self.connected:
            return None

        try:
            # 尝试读取protobuf格式的数据
            entry = self._read_protobuf_entry()
            if entry:
                return self._convert_protobuf_to_dict(entry)

            # 如果没有真实数据，直接返回None
            # 不生成任何模拟数据，确保测试的真实性
            await asyncio.sleep(timeout)
            return None

        except Exception as e:
            logger.error(f"Error reading telemetry entry: {e}")
            return None

    def _read_protobuf_entry(self) -> TelemetryEntry | None:
        """从共享内存读取protobuf格式的遥测条目"""
        if not self.mmap_obj:
            return None

        try:
            # 获取当前写位置以检查可用数据
            write_pos = struct.unpack("<Q", self.mmap_obj[8:16])[0]

            # 检查是否有足够的数据读取长度字段
            if self.read_position + 4 > write_pos:
                return None

            # 计算在内存映射中的实际位置（跳过64字节元数据头部）
            METADATA_SIZE = 64
            actual_pos = METADATA_SIZE + self.read_position

            # 检查实际位置是否在文件范围内
            if actual_pos + 4 > len(self.mmap_obj):
                return None

            # 读取protobuf消息长度
            length_bytes = self.mmap_obj[actual_pos : actual_pos + 4]
            entry_length = struct.unpack("<I", length_bytes)[0]

            # 检查长度是否合理
            if entry_length <= 0 or entry_length > 10000:
                logger.debug(f"Invalid entry length: {entry_length}")
                return None

            # 检查是否有足够的数据读取完整条目
            if self.read_position + 4 + entry_length > write_pos:
                return None

            # 读取protobuf数据
            data_start = actual_pos + 4
            entry_data = self.mmap_obj[data_start : data_start + entry_length]

            # 解析protobuf消息
            entry = TelemetryEntry()
            entry.ParseFromString(entry_data)

            # 更新读取位置（这是相对于数据区的逻辑位置）
            self.read_position += 4 + entry_length

            return entry

        except Exception as e:
            logger.debug(f"Error reading protobuf entry: {e}")
            return None

    def _convert_protobuf_to_dict(self, entry: TelemetryEntry) -> dict[str, Any]:
        """将protobuf条目转换为字典格式"""
        # 基础信息
        result = {
            "type": TelemetryDataType.Name(entry.data_type).lower(),
            "instance_id": entry.instance_id,
            "timestamp": entry.timestamp_ns / 1_000_000_000.0,  # 转换为秒
            "data_type": entry.data_type,  # 添加数值类型供处理器使用
        }

        # 根据数据类型解析payload - 保持嵌套结构以兼容TelemetryAggregator
        payload_type = entry.WhichOneof("payload")
        if payload_type == "execution_stats":
            stats = entry.execution_stats
            result["execution_stats"] = {
                "executions": stats.executions,
                "exec_per_sec": stats.exec_per_sec,
                "corpus_size": stats.corpus_size,
                "crashes": stats.crashes,
            }
        elif payload_type == "coverage_hit":
            hit = entry.coverage_hit
            result["coverage_hit"] = {
                "edge_id": hit.edge_id,
                "hit_count": hit.hit_count,
                "is_new": hit.is_new,
            }
        elif payload_type == "crash_found":
            crash = entry.crash_found
            result["crash_found"] = {
                "crash_type": crash.crash_type,
                "input_hash": crash.input_hash,
                "signal": crash.signal,
            }
        elif payload_type == "corpus_grow":
            grow = entry.corpus_grow
            result["corpus_grow"] = {
                "new_inputs": grow.new_inputs,
                "total_size": grow.total_size,
                "avg_length": grow.avg_length,
            }
        elif payload_type == "mutator_stats":
            stats = entry.mutator_stats
            result["mutator_stats"] = {
                "mutator_id": stats.mutator_id,
                "usage_count": stats.usage_count,
                "success_rate": stats.success_rate,
            }

        return result

    def _get_possible_telemetry_paths(self, stream_name: str) -> list[str]:
        """获取所有可能的遥测文件路径

        Args:
            stream_name: 流名称

        Returns:
            可能的文件路径列表，按优先级排序
        """
        import os

        paths = []

        # 1. 当前配置的路径
        if hasattr(self, "shm_path") and self.shm_path:
            paths.append(str(self.shm_path))

        # 2. 基于当前base_path的路径
        if hasattr(self, "base_path") and self.base_path:
            paths.append(os.path.join(self.base_path, stream_name))

        # 3. fuzzing-engine目录下的runtime/temp (Rust进程工作目录)
        current_dir = os.getcwd()
        if "fuzzlm-agent" in current_dir:
            parts = current_dir.split(os.sep)
            for i, part in enumerate(parts):
                if part == "fuzzlm-agent":
                    project_root = os.sep.join(parts[: i + 1])
                    fuzzing_engine_path = os.path.join(
                        project_root,
                        "fuzzlm_agent",
                        "fuzzing-engine",
                        "runtime",
                        "temp",
                        stream_name,
                    )
                    paths.append(fuzzing_engine_path)
                    break

        # 4. 项目根目录下的runtime/temp
        if "fuzzlm-agent" in current_dir:
            parts = current_dir.split(os.sep)
            for i, part in enumerate(parts):
                if part == "fuzzlm-agent":
                    project_root = os.sep.join(parts[: i + 1])
                    root_runtime_path = os.path.join(
                        project_root, "runtime", "temp", stream_name
                    )
                    paths.append(root_runtime_path)
                    break

        # 5. 环境变量指定的路径
        shm_dir = os.environ.get("FUZZLM_SHM_DIR")
        if shm_dir:
            paths.append(os.path.join(shm_dir, stream_name))

        # 6. 当前目录下的runtime/temp
        paths.append(os.path.join(current_dir, "runtime", "temp", stream_name))

        # 去重并保持顺序
        seen = set()
        unique_paths = []
        for path in paths:
            if path not in seen:
                seen.add(path)
                unique_paths.append(path)

        return unique_paths

    async def read_batch(self, max_entries: int = 100) -> list[dict[str, Any]]:
        """批量读取遥测条目

        Args:
            max_entries: 最大读取条目数

        Returns:
            遥测条目列表

        """
        entries = []
        for _ in range(max_entries):
            entry = await self.read_entry(timeout=0.001)
            if entry is None:
                break
            entries.append(entry)
        return entries

    def _create_test_shm(self) -> None:
        """创建测试用的共享内存文件"""
        try:
            # 创建共享内存目录(如果不存在)
            self.shm_path.parent.mkdir(parents=True, exist_ok=True)

            # 创建并初始化共享内存文件
            with open(self.shm_path, "wb") as f:
                # 写入头部 - protobuf版本
                header = struct.pack(
                    "<IIII",  # 魔数、版本、保留、保留
                    self.MAGIC_NUMBER,
                    self.VERSION,
                    0,  # 保留字段
                    0,  # 保留字段
                )
                f.write(header)

                # 预分配空间
                f.write(b"\x00" * (1024 * 1024))  # 1MB数据区域

            logger.info(f"Created test shared memory file: {self.shm_path} (protobuf)")

        except Exception as e:
            logger.error(f"Failed to create test shared memory: {e}")
            raise

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息

        Returns:
            统计信息字典

        """
        return {
            "connected": self.connected,
            "stream_name": self.stream_name,
            "shm_path": str(self.shm_path),
            "read_position": self.read_position,
            "format": "protobuf",
            "version": self.VERSION,
        }


class TelemetryReader:
    """遥测读取器 - 提供给Phase 3和Phase 4使用的高级接口

    封装了TelemetryDataPlane, 提供更简单的API。
    """

    def __init__(self, stream_name: str = "fuzzlm_telemetry"):
        """初始化遥测读取器

        Args:
            stream_name: 共享内存流名称

        """
        self.data_plane = TelemetryDataPlane(stream_name)
        self.connected = False

    async def connect(self, max_retries: int = 20, retry_delay: float = 1.0) -> bool:
        """连接到遥测流，支持robust重试机制

        Args:
            max_retries: 最大重试次数
            retry_delay: 重试间隔(秒)

        Returns:
            是否成功连接
        """
        for attempt in range(max_retries + 1):
            self.connected = self.data_plane.connect()
            if self.connected:
                logger.info(
                    f"Successfully connected to telemetry stream: {self.data_plane.stream_name}"
                )
                return True

            if attempt < max_retries:
                # 使用指数退避，但有最大限制
                delay = min(retry_delay * (1.2**attempt), 3.0)
                logger.debug(
                    f"Connection attempt {attempt + 1}/{max_retries + 1} failed, retrying in {delay:.1f}s..."
                )
                await asyncio.sleep(delay)
            else:
                logger.error(
                    f"Failed to connect to telemetry stream after {max_retries + 1} attempts"
                )

        return False

    async def disconnect(self) -> None:
        """断开连接"""
        self.data_plane.disconnect()
        self.connected = False

    async def read_entry(self, timeout: float = 0.01) -> dict[str, Any] | None:
        """读取单个遥测条目"""
        if not self.connected:
            return None
        return await self.data_plane.read_entry(timeout)

    async def read_batch(self, max_entries: int = 100) -> list[dict[str, Any]]:
        """批量读取遥测条目"""
        if not self.connected:
            return []
        return await self.data_plane.read_batch(max_entries)


# 辅助函数
async def create_telemetry_reader(
    stream_name: str = "fuzzlm_telemetry",
) -> TelemetryReader:
    """创建并连接遥测读取器

    Args:
        stream_name: 共享内存流名称

    Returns:
        已连接的TelemetryReader实例

    """
    reader = TelemetryReader(stream_name)
    await reader.connect()
    return reader


# 向后兼容性工具类
class TelemetryDataTypeCompat:
    """保持向后兼容性的数据类型枚举工具"""

    @classmethod
    def from_protobuf(cls, pb_type: int) -> str:
        """从protobuf类型转换为字符串"""
        return str(TelemetryDataType.Name(pb_type)).lower()

    @classmethod
    def to_protobuf(cls, type_str: str) -> int:
        """从字符串转换为protobuf类型"""
        return int(getattr(TelemetryDataType, type_str.upper()))
