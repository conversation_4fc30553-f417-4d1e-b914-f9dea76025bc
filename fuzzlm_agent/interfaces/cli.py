"""FuzzLM-Agent CLI - 简化的研究原型版本"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Any, Optional

from rich.console import Console

from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignOrchestrator


class FuzzLMCLI:
    """FuzzLM-Agent 简化CLI - 研究原型版"""

    def __init__(self) -> None:
        self.console = Console()
        self.orchestrator: Optional[CampaignOrchestrator] = None
        self._setup_logging()

    def _setup_logging(self) -> None:
        """设置日志系统"""
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        logging.basicConfig(
            format=log_format,
            level=logging.INFO,
            handlers=[logging.StreamHandler(sys.stdout)],
        )

    def create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="FuzzLM-Agent - 智能模糊测试框架(研究原型)",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例:
  python -m fuzzlm_agent start target.c                    # 运行2小时
  python -m fuzzlm_agent start target.c --duration 4.0    # 运行4小时
  python -m fuzzlm_agent start target.c --unlimited       # 无限运行
            """,
        )

        subparsers = parser.add_subparsers(dest="command", help="可用命令")

        # start 命令
        start_parser = subparsers.add_parser("start", help="启动模糊测试Campaign")
        start_parser.add_argument("target_path", help="目标文件路径")
        start_parser.add_argument(
            "--duration", type=float, default=2.0, help="运行时长(小时,默认2.0)"
        )
        start_parser.add_argument(
            "--unlimited", action="store_true", help="无限运行模式"
        )
        start_parser.add_argument(
            "--config", default="config.yaml", help="配置文件路径(默认config.yaml)"
        )

        return parser

    def run(self, args: Optional[list[str]] = None) -> None:
        """运行CLI"""
        parser = self.create_parser()
        if not args:
            args = sys.argv[1:]

        if not args:
            parser.print_help()
            return

        try:
            parsed_args = parser.parse_args(args)

            if parsed_args.command == "start":
                self.handle_start_command(parsed_args)
            else:
                parser.print_help()

        except KeyboardInterrupt:
            self.console.print("\n[yellow]操作被用户中断[/yellow]")
        except Exception as e:
            self.console.print(f"[red]错误: {e}[/red]")
            raise

    def handle_start_command(self, args: Any) -> None:
        """处理start命令"""
        target_path = Path(args.target_path)

        # 验证目标路径
        if not target_path.exists():
            self.console.print(f"[red]错误: 目标路径不存在: {target_path}[/red]")
            return

        self.console.print(f"[green]启动模糊测试: {target_path}[/green]")

        # 创建编排器并运行Campaign
        self.orchestrator = CampaignOrchestrator()

        # 设置运行模式
        if args.unlimited:
            duration_hours = float("inf")  # 使用无穷大表示无限运行
            self.console.print("[yellow]运行模式: 无限运行[/yellow]")
        else:
            duration_hours = float(args.duration)
            self.console.print(f"[yellow]运行模式: {duration_hours}小时[/yellow]")

        try:
            result = self.orchestrator.run_campaign(
                target_path=str(target_path),
                hours=duration_hours,
            )
            self.console.print(f"[green]Campaign完成: {result}[/green]")
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Campaign被用户中断[/yellow]")
        except Exception as e:
            self.console.print(f"[red]Campaign执行失败: {e}[/red]")
            raise


def main() -> None:
    """CLI入口点"""
    cli = FuzzLMCLI()
    cli.run()


if __name__ == "__main__":
    main()
