"""Campaign编排器
================================

实现docs/workflow.md中定义的核心工作流, 采用简化架构设计。
直接函数调用, 无事件总线, 无状态机, 同步优先。

主要职责:
- 管理Campaign完整生命周期(Phase 0-5)
- 内联实现简单phases(0, 2, 5)
- 调用独立模块处理复杂phases(1, 3, 4)
- 简单错误处理和日志记录

设计原则:
- 简洁性优先: 直接实现而非过度抽象
- 错误恢复: 优雅处理失败场景
- 资源管理: 自动清理和资源释放
- 可观测性: 全面的日志和监控
"""

from __future__ import annotations

import asyncio
import json
import logging
import os
import shutil
import traceback
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from pathlib import Path
from typing import Any

import yaml

from fuzzlm_agent.domain.schemas import CampaignResult
from fuzzlm_agent.infrastructure.id_generator import generate_campaign_id
from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient
from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient
from fuzzlm_agent.infrastructure.shared_memory import TelemetryReader
from fuzzlm_agent.knowledge.simple_kb import SimpleKnowledgeBase
from fuzzlm_agent.orchestrator.validation import (
    validate_custom_code,
    validate_strategy_parameters,
    validate_strategy_structure,
)


class CampaignStatus(Enum):
    """Campaign运行状态枚举"""

    INITIALIZING = "initializing"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PhaseError(Exception):
    """Phase执行异常基类"""

    def __init__(self, phase: str, message: str, cause: Exception | None = None):
        self.phase = phase
        self.cause = cause
        super().__init__(f"Phase {phase} error: {message}")


@dataclass
class CampaignContext:
    """贯穿整个Campaign的上下文数据

    Attributes:
        campaign_id: 唯一的Campaign标识符
        target_path: 目标程序路径
        duration_hours: 运行时长(小时)
        config: 全局配置字典
        workspace: 工作空间路径
        strategy: 生成的模糊测试策略
        champion_id: 当前Champion进程ID
        final_metrics: 最终性能指标
        reflection: Phase 5反思结果
        status: 当前运行状态
        error: 错误信息(如果有)
        start_time: 开始时间
        metadata: 额外元数据
        elapsed_hours: 已运行时间
        needs_adjustment: 是否需要策略调整
    """

    campaign_id: str
    target_path: str
    duration_hours: float
    config: dict[str, Any] = field(default_factory=dict)

    # Phase产出
    workspace: str | None = None
    strategy: dict[str, Any] | None = None
    champion_id: str | None = None
    final_metrics: dict[str, Any] = field(default_factory=dict)
    reflection: str | None = None

    # 状态
    status: CampaignStatus = CampaignStatus.RUNNING
    error: str | None = None
    start_time: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    # 额外属性用于phase模块
    metadata: dict[str, Any] = field(default_factory=dict)
    elapsed_hours: float = 0.0
    needs_adjustment: bool = False

    # 新增: 性能追踪
    phase_timings: dict[str, float] = field(default_factory=dict)
    resource_usage: dict[str, Any] = field(default_factory=dict)

    def to_result(self) -> CampaignResult:
        """转换为CampaignResult对象

        Returns:
            CampaignResult: 包含Campaign执行结果的对象
        """
        return CampaignResult(
            campaign_id=self.campaign_id,
            success=self.status == CampaignStatus.COMPLETED,
            start_time=self.start_time,
            end_time=datetime.now(timezone.utc),
            final_coverage=self.final_metrics.get("coverage", 0.0),
            unique_crashes=self.final_metrics.get("unique_crashes", 0),
            total_executions=self.final_metrics.get("total_executions", 0),
            strategy_changes=self.final_metrics.get("strategy_changes", 0),
            phases_completed=self.final_metrics.get("phases_completed", 0),
            error_message=self.error,
            metadata={
                "workspace": self.workspace,
                "reflection": self.reflection,
                "phase_timings": self.phase_timings,
                "resource_usage": self.resource_usage,
            },
        )


class CampaignOrchestrator:
    """Campaign编排器

    核心编排器类, 负责管理整个fuzzing campaign的生命周期。
    采用简化设计, 直接调用各phase实现, 无复杂状态机。
    """

    def __init__(self, config_path: str = "config.yaml") -> None:
        """初始化编排器

        Args:
            config_path: 配置文件路径

        Raises:
            RuntimeError: 组件初始化失败时抛出
        """
        self.logger = logging.getLogger(__name__)

        # 加载配置
        self.config = self._load_config(config_path)

        # 验证配置完整性
        self._validate_config()

        # 初始化基础设施组件
        self.llm_client: LiteLLMClient | None = None
        self.runtime_client: RuntimeClient | None = None
        self.telemetry_reader: TelemetryReader | None = None
        self.knowledge_base: SimpleKnowledgeBase | None = None

        # 运行状态跟踪
        self._active_campaign: CampaignContext | None = None
        self._cleanup_handlers: list[asyncio.Task[Any]] = []

        # 初始化组件
        self._initialize_components()

    def _load_config(self, config_path: str) -> dict[str, Any]:
        """加载配置文件

        Args:
            config_path: 配置文件路径

        Returns:
            Dict[str, Any]: 配置字典
        """
        try:
            config_file = Path(config_path)
            if not config_file.exists():
                self.logger.warning(
                    f"Config file not found: {config_path}, using defaults"
                )
                return self._get_default_config()

            with open(config_file) as f:
                config = yaml.safe_load(f)
                if not isinstance(config, dict):
                    self.logger.warning("Invalid config format, using defaults")
                    return self._get_default_config()

                # 合并默认值
                return self._merge_with_defaults(config)

        except Exception as e:
            self.logger.warning(f"Failed to load config from {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> dict[str, Any]:
        """获取默认配置"""
        return {
            "llm": {
                "provider": "openrouter",
                "model": "anthropic/claude-3.5-sonnet",
                "api_key": os.getenv("OPENROUTER_API_KEY", ""),
                "temperature": 0.1,
                "max_retries": 3,
            },
            "grpc": {
                "address": "localhost:50051",
                "timeout": 30.0,
                "max_retries": 3,
            },
            "knowledge_base": {
                "path": "./data/knowledge_base.db",
                "enable_rag": True,
            },
            "system": {
                "workspace_root": "./workspace",
                "default_duration_hours": 2.0,
                "cleanup_workspace": False,
                "resource_limits": {
                    "max_memory_mb": 4096,
                    "max_cpu_percent": 80,
                },
            },
            "fuzzing": {
                "seed_count": 6,
                "enable_llm_mutator_generation": True,
            },
        }

    def _merge_with_defaults(self, config: dict[str, Any]) -> dict[str, Any]:
        """合并用户配置与默认配置"""
        defaults = self._get_default_config()

        def deep_merge(base: dict[str, Any], update: dict[str, Any]) -> dict[str, Any]:
            for key, value in update.items():
                if (
                    key in base
                    and isinstance(base[key], dict)
                    and isinstance(value, dict)
                ):
                    base[key] = deep_merge(base[key], value)
                else:
                    base[key] = value
            return base

        return deep_merge(defaults, config)

    def _validate_config(self) -> None:
        """验证配置完整性"""
        required_paths = [
            "llm.provider",
            "llm.model",
            "grpc.address",
            "system.workspace_root",
        ]

        for path in required_paths:
            keys = path.split(".")
            value = self.config
            for key in keys:
                if key not in value:
                    raise ValueError(f"Missing required config: {path}")
                value = value[key]

    def _initialize_components(self) -> None:
        """初始化基础设施组件

        Raises:
            RuntimeError: 任何组件初始化失败时抛出
        """
        errors = []

        try:
            # 创建LLM客户端
            llm_config = self.config.get("llm", {})
            self.llm_client = LiteLLMClient(llm_config)
            self.logger.info("LLM client initialized")
        except Exception as e:
            errors.append(f"LLM client: {e}")
            self.logger.error(f"Failed to initialize LLM client: {e}")

        try:
            # 创建运行时客户端
            grpc_config = self.config.get("grpc", {})
            self.runtime_client = RuntimeClient(
                {
                    "server_address": grpc_config.get("address", "localhost:50051"),
                    "timeout": grpc_config.get("timeout", 30.0),
                    "max_retries": grpc_config.get("max_retries", 3),
                },
            )
            self.logger.info("Runtime client initialized")
        except Exception as e:
            errors.append(f"Runtime client: {e}")
            self.logger.error(f"Failed to initialize Runtime client: {e}")

        try:
            # 创建遥测读取器
            # TelemetryReader will be initialized in phase3 with correct instance_id
            self.logger.info("Telemetry reader will be initialized in phase3")
        except Exception as e:
            errors.append(f"Telemetry reader: {e}")
            self.logger.error(f"Failed to initialize Telemetry reader: {e}")

        try:
            # 创建知识库
            kb_config = self.config.get("knowledge_base", {})
            kb_path = kb_config.get("path", "./data/knowledge_base.db")

            # 确保知识库目录存在
            kb_dir = Path(kb_path).parent
            kb_dir.mkdir(parents=True, exist_ok=True)

            self.knowledge_base = SimpleKnowledgeBase(db_path=kb_path)
            self.logger.info(f"Knowledge base initialized at {kb_path}")
        except Exception as e:
            errors.append(f"Knowledge base: {e}")
            self.logger.error(f"Failed to initialize Knowledge base: {e}")

        # 如果有任何关键组件失败, 抛出异常
        if errors:
            raise RuntimeError(f"Component initialization failed: {'; '.join(errors)}")

        self.logger.info("All components initialized successfully")

    def run_campaign(self, target_path: str, hours: float = 2.0) -> CampaignResult:
        """同步接口运行Campaign

        Args:
            target_path: 目标程序路径
            hours: 运行时长(小时)

        Returns:
            CampaignResult: Campaign执行结果

        Raises:
            ValueError: 无效的参数
            RuntimeError: 运行时错误
        """
        # 参数验证
        if not target_path:
            raise ValueError("Target path cannot be empty")

        if hours <= 0:
            raise ValueError(f"Duration must be positive, got {hours}")

        # 使用asyncio.run处理异步
        try:
            return asyncio.run(self._run_async(target_path, hours))
        except KeyboardInterrupt:
            self.logger.warning("Campaign interrupted by user")
            if self._active_campaign:
                self._active_campaign.status = CampaignStatus.CANCELLED
                return self._active_campaign.to_result()
            raise
        except Exception as e:
            self.logger.error(f"Campaign failed with unexpected error: {e}")
            raise

    async def _run_async(self, target_path: str, hours: float) -> CampaignResult:
        """实际的异步执行逻辑

        Args:
            target_path: 目标程序路径
            hours: 运行时长

        Returns:
            CampaignResult: Campaign结果
        """
        import time

        start_time = time.time()

        # 创建Campaign上下文
        ctx = CampaignContext(
            campaign_id=generate_campaign_id(target_path, self.config),
            target_path=target_path,
            duration_hours=hours,
            config=self.config,
        )

        self._active_campaign = ctx
        self.logger.info(
            f"Starting campaign {ctx.campaign_id} for {target_path} "
            f"with {hours} hours duration"
        )

        try:
            # 连接基础设施
            await self._connect_infrastructure()

            # 执行各个phase
            await self._execute_phases(ctx, hours)

            # 标记完成
            ctx.status = CampaignStatus.COMPLETED
            self.logger.info(f"Campaign {ctx.campaign_id} completed successfully")

        except asyncio.CancelledError:
            ctx.status = CampaignStatus.CANCELLED
            ctx.error = "Campaign was cancelled"
            self.logger.warning(f"Campaign {ctx.campaign_id} was cancelled")
            raise

        except PhaseError as e:
            ctx.status = CampaignStatus.FAILED
            ctx.error = str(e)
            self.logger.error(f"Campaign {ctx.campaign_id} failed in {e.phase}: {e}")
            if e.cause:
                self.logger.error(f"Caused by: {e.cause}")

        except Exception as e:
            ctx.status = CampaignStatus.FAILED
            ctx.error = str(e)
            self.logger.error(f"Campaign {ctx.campaign_id} failed: {e}")
            self.logger.error(traceback.format_exc())

        finally:
            # 记录总运行时间
            ctx.elapsed_hours = (time.time() - start_time) / 3600

            # 清理资源
            await self._cleanup_resources(ctx)

            self._active_campaign = None

        return ctx.to_result()

    async def _connect_infrastructure(self) -> None:
        """连接基础设施组件

        Raises:
            RuntimeError: 连接失败时抛出
        """
        connection_errors = []

        # 连接Runtime客户端
        if self.runtime_client:
            try:
                await self.runtime_client.connect()
                health = await self.runtime_client.health_check()
                self.logger.info(f"Runtime client connected, health: {health}")
            except Exception as e:
                connection_errors.append(f"Runtime client: {e}")
                self.logger.error(f"Failed to connect runtime client: {e}")

        # 连接遥测读取器
        if self.telemetry_reader:
            try:
                connected = await self.telemetry_reader.connect()
                if connected:
                    self.logger.info("Telemetry reader connected")
                else:
                    connection_errors.append("Telemetry reader: connection failed")
                    self.logger.warning("Failed to connect telemetry reader")
            except Exception as e:
                connection_errors.append(f"Telemetry reader: {e}")
                self.logger.error(f"Error connecting telemetry reader: {e}")

        # 如果有关键连接失败, 抛出异常
        if connection_errors:
            raise RuntimeError(
                f"Failed to connect infrastructure: {'; '.join(connection_errors)}"
            )

    async def _execute_phases(self, ctx: CampaignContext, hours: float) -> None:
        """执行所有phase

        Args:
            ctx: Campaign上下文
            hours: 运行时长

        Raises:
            PhaseError: Phase执行失败时抛出
        """
        import time

        # Phase 0: 系统初始化
        phase_start = time.time()
        try:
            ctx = self._phase0_init(ctx)
            ctx.final_metrics["phases_completed"] = 1
            ctx.phase_timings["phase0"] = time.time() - phase_start
            self.logger.info(f"Phase 0 completed in {ctx.phase_timings['phase0']:.2f}s")
        except Exception as e:
            raise PhaseError("0", "System initialization failed", e) from e

        # Phase 1: 策略生成
        phase_start = time.time()
        try:
            from .phase1_strategy import phase1_generate_strategy

            if self.llm_client is None:
                raise RuntimeError("LLM client is not initialized")

            ctx = await phase1_generate_strategy(
                ctx,
                self.llm_client,
                self.knowledge_base,
            )
            ctx.final_metrics["phases_completed"] = 2
            ctx.final_metrics["strategy_changes"] = 0
            ctx.phase_timings["phase1"] = time.time() - phase_start
            self.logger.info(f"Phase 1 completed in {ctx.phase_timings['phase1']:.2f}s")
        except Exception as e:
            raise PhaseError("1", "Strategy generation failed", e) from e

        # Phase 2: 验证
        phase_start = time.time()
        try:
            ctx = self._phase2_validate(ctx)
            ctx.final_metrics["phases_completed"] = 3
            ctx.phase_timings["phase2"] = time.time() - phase_start
            self.logger.info(f"Phase 2 completed in {ctx.phase_timings['phase2']:.2f}s")
        except Exception as e:
            raise PhaseError("2", "Strategy validation failed", e) from e

        # Phase 3: 生产运行
        phase_start = time.time()
        try:
            from .phase3_production import phase3_production_run

            if self.runtime_client is None:
                raise RuntimeError("Runtime client not initialized")

            ctx = await phase3_production_run(
                ctx,
                self.runtime_client,
                self.llm_client,
                hours,
            )
            ctx.final_metrics["phases_completed"] = 4
            ctx.phase_timings["phase3"] = time.time() - phase_start
            self.logger.info(f"Phase 3 completed in {ctx.phase_timings['phase3']:.2f}s")
        except Exception as e:
            raise PhaseError("3", "Production run failed", e) from e

        # Phase 4: Shadow testing is integrated within Phase 3
        ctx.final_metrics["phases_completed"] = 5

        # Phase 5: 反思学习
        phase_start = time.time()
        try:
            ctx = await self._phase5_reflect(ctx)
            ctx.final_metrics["phases_completed"] = 6
            ctx.phase_timings["phase5"] = time.time() - phase_start
            self.logger.info(f"Phase 5 completed in {ctx.phase_timings['phase5']:.2f}s")
        except Exception as e:
            # Phase 5失败不应该导致整个campaign失败
            self.logger.error(f"Phase 5 reflection failed: {e}")
            ctx.reflection = json.dumps({"error": str(e)})

    async def _cleanup_resources(self, ctx: CampaignContext) -> None:
        """清理资源

        Args:
            ctx: Campaign上下文
        """
        cleanup_tasks = []

        # 断开遥测读取器
        if self.telemetry_reader:
            cleanup_tasks.append(
                self._safe_cleanup(
                    self.telemetry_reader.disconnect(), "telemetry reader"
                )
            )

        # 断开运行时客户端
        if self.runtime_client:
            cleanup_tasks.append(
                self._safe_cleanup(self.runtime_client.disconnect(), "runtime client")
            )

        # 等待所有清理任务完成
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)

        # 清理工作空间
        if ctx.workspace and self.config.get("system", {}).get(
            "cleanup_workspace", False
        ):
            await asyncio.to_thread(self._cleanup_workspace, ctx.workspace)

    async def _safe_cleanup(self, coro: Any, resource_name: str) -> None:
        """安全执行清理操作

        Args:
            coro: 清理协程
            resource_name: 资源名称
        """
        try:
            await asyncio.wait_for(coro, timeout=5.0)
            self.logger.debug(f"Cleaned up {resource_name}")
        except asyncio.TimeoutError:
            self.logger.warning(f"Timeout cleaning up {resource_name}")
        except Exception as e:
            self.logger.error(f"Error cleaning up {resource_name}: {e}")

    def _phase0_init(self, ctx: CampaignContext) -> CampaignContext:
        """Phase 0: 系统初始化 - 简单内联实现

        Args:
            ctx: Campaign上下文

        Returns:
            CampaignContext: 更新后的上下文

        Raises:
            FileNotFoundError: 目标文件不存在
            ValueError: 不支持的文件类型
        """
        self.logger.info("Phase 0: System initialization")

        # 1. 验证目标文件
        target = Path(ctx.target_path)
        if not target.exists():
            raise FileNotFoundError(f"Target not found: {ctx.target_path}")

        # 支持的文件类型
        supported_suffixes = {".c", ".cpp", ".cc", ".cxx", ".c++"}
        if target.suffix.lower() not in supported_suffixes:
            raise ValueError(
                f"Unsupported target type: {target.suffix}. "
                f"Supported types: {', '.join(supported_suffixes)}"
            )

        # 2. 创建工作空间
        workspace_root = Path(
            self.config.get("system", {}).get(
                "workspace_root",
                "./workspace",
            )
        )
        ctx.workspace = str(workspace_root / ctx.campaign_id)
        workspace_path = Path(ctx.workspace)

        try:
            workspace_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created workspace at {ctx.workspace}")
        except Exception as e:
            raise RuntimeError(f"Failed to create workspace: {e}") from e

        # 3. 复制目标到工作空间
        target_copy = workspace_path / "target" / target.name
        target_copy.parent.mkdir(exist_ok=True)

        try:
            shutil.copy2(ctx.target_path, target_copy)
            self.logger.info(f"Copied target to {target_copy}")
        except Exception as e:
            raise RuntimeError(f"Failed to copy target: {e}") from e

        # 4. 创建种子目录
        seeds_dir = workspace_path / "seeds"
        seeds_dir.mkdir(exist_ok=True)

        # 5. 创建默认种子
        seed_count = self.config.get("fuzzing", {}).get("seed_count", 6)
        default_seeds = self._generate_default_seeds(seed_count)

        for i, seed in enumerate(default_seeds):
            seed_path = seeds_dir / f"seed_{i:03d}"
            seed_path.write_bytes(seed)

        self.logger.info(
            f"Workspace initialized with {len(default_seeds)} default seeds"
        )

        return ctx

    def _generate_default_seeds(self, count: int) -> list[bytes]:
        """生成默认种子

        Args:
            count: 种子数量

        Returns:
            List[bytes]: 种子列表
        """
        # 基础种子集
        seeds = [
            b"",  # 空种子
            b"A",  # 单字符
            b"ABC",  # 短字符串
            b"1234567890",  # 数字
            b"\x00\x01\x02\x03",  # 二进制
            b"A" * 100,  # 重复模式
            b"\x00" * 50,  # NULL字节
            b"\xff" * 50,  # 最大值字节
        ]
        # 例如: JSON、XML、协议特定格式等

        return seeds[:count]

    def _phase2_validate(self, ctx: CampaignContext) -> CampaignContext:
        """Phase 2: 策略验证 - 实现复合验证流程"""
        self.logger.info("Phase 2: Strategy validation with compound verification")

        if not ctx.strategy:
            msg = "No strategy generated in Phase 1"
            raise ValueError(msg)

        # 1. 基本结构验证
        self._validate_strategy_structure(ctx.strategy)

        # 2. 基本可行性检查

        # 3. 如果有自定义代码, 执行复合验证
        if "custom_code" in ctx.strategy:
            self._validate_custom_code(ctx)

        # 4. 验证策略配置的合理性
        self._validate_strategy_parameters(ctx.strategy)

        self.logger.info("Strategy validated successfully")

        return ctx

    def _validate_strategy_structure(self, strategy: dict[str, Any]) -> None:
        """验证策略的基本结构

        Args:
            strategy: 策略字典

        Raises:
            ValueError: 策略结构无效时抛出
        """
        # 使用新的验证模块
        is_valid, error_msg = validate_strategy_structure(strategy)
        if not is_valid:
            raise ValueError(error_msg)

    def _validate_custom_code(self, ctx: CampaignContext) -> None:
        """验证自定义代码

        Args:
            ctx: Campaign上下文
        """
        if ctx.strategy is None:
            return

        custom_code = ctx.strategy.get("custom_code", "")
        rust_mutator_code = ctx.strategy.get("rust_mutator_code", "")

        if not custom_code and not rust_mutator_code:
            return

        self.logger.info("Validating custom code...")

        # 使用新的验证模块进行安全检查
        warnings = validate_custom_code(custom_code, rust_mutator_code)

        if warnings:
            self.logger.warning(
                f"Potentially dangerous patterns found in code: {'; '.join(warnings)}"
            )

        self.logger.info("Custom code validation completed")

    def _validate_strategy_parameters(self, strategy: dict[str, Any]) -> None:
        """验证策略参数的合理性

        Args:
            strategy: 策略字典
        """
        # 使用新的验证模块
        warnings = validate_strategy_parameters(strategy)

        # 记录任何警告
        for warning in warnings:
            self.logger.warning(warning)

    async def _phase5_reflect(self, ctx: CampaignContext) -> CampaignContext:
        """Phase 5: 反思学习 - 使用核心反思引擎

        Args:
            ctx: Campaign上下文

        Returns:
            CampaignContext: 更新后的上下文
        """
        self.logger.info("Phase 5: Reflective learning with core engine")

        try:
            # 收集反思数据
            reflection_data = await self._gather_reflection_data(ctx)

            # 生成反思洞察
            insights = await self._generate_insights(reflection_data)

            # 存储反思结果
            ctx.reflection = json.dumps(insights, indent=2)

            # 如果有知识库, 存储经验
            if self.knowledge_base:
                await self._store_experience(ctx, insights)

            self.logger.info(
                f"Reflection completed with confidence score: "
                f"{insights.get('confidence_score', 0.0):.2f}"
            )

        except Exception as e:
            self.logger.error(f"Reflection failed: {e}", exc_info=True)
            # 降级处理: 存储基本信息
            ctx.reflection = json.dumps(
                {
                    "error": str(e),
                    "basic_metrics": ctx.final_metrics,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            )

        return ctx

    async def _gather_reflection_data(self, ctx: CampaignContext) -> dict[str, Any]:
        """收集反思所需的数据

        Args:
            ctx: Campaign上下文

        Returns:
            Dict[str, Any]: 反思数据
        """
        return {
            "campaign_id": ctx.campaign_id,
            "target": Path(ctx.target_path).name,
            "duration_hours": ctx.duration_hours,
            "elapsed_hours": ctx.elapsed_hours,
            "strategy": ctx.strategy,
            "metrics": ctx.final_metrics,
            "phase_timings": ctx.phase_timings,
            "resource_usage": ctx.resource_usage,
            "status": ctx.status.value,
            "error": ctx.error,
        }

    async def _generate_insights(self, data: dict[str, Any]) -> dict[str, Any]:
        """生成反思洞察

        Args:
            data: 反思数据

        Returns:
            Dict[str, Any]: 洞察结果
        """
        # 简化的洞察生成
        insights: dict[str, Any] = {
            "insights": [],
            "improvements": [],
            "generalizable_lessons": [],
            "state_awareness_adjustments": [],
            "identified_patterns": [],
            "confidence_score": 0.7,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # 基于指标生成洞察
        coverage = data["metrics"].get("coverage", 0.0)
        crashes = data["metrics"].get("unique_crashes", 0)

        if coverage > 0.8:
            insights["insights"].append("Achieved high code coverage")
            insights["confidence_score"] = 0.9
        elif coverage < 0.3:
            insights["insights"].append("Low code coverage indicates potential issues")
            insights["improvements"].append("Consider adjusting mutator strategy")
            insights["confidence_score"] = 0.5

        if crashes > 0:
            insights["insights"].append(f"Found {crashes} unique crashes")
            insights["generalizable_lessons"].append(
                "Current strategy effective for crash detection"
            )

        # 分析phase时间
        if data.get("phase_timings"):
            slowest_phase = max(data["phase_timings"].items(), key=lambda x: x[1])
            insights["identified_patterns"].append(
                f"Phase {slowest_phase[0]} took longest ({slowest_phase[1]:.2f}s)"
            )

        return insights

    async def _store_experience(
        self, ctx: CampaignContext, insights: dict[str, Any]
    ) -> None:
        """存储经验到知识库

        Args:
            ctx: Campaign上下文
            insights: 洞察结果
        """
        try:
            experience_data = {
                "campaign_id": ctx.campaign_id,
                "target": Path(ctx.target_path).name,
                "target_path": ctx.target_path,
                "strategy": ctx.strategy or {},
                "metrics": ctx.final_metrics,
                "insights": insights,
                "phase_timings": ctx.phase_timings,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "duration_hours": ctx.duration_hours,
                "success": ctx.status == CampaignStatus.COMPLETED,
            }

            # 存储到知识库
            if self.knowledge_base is not None:
                await self.knowledge_base.store_simple_experience(
                    ctx.campaign_id,
                    experience_data,
                )
                self.logger.info(
                    f"Stored experience for campaign {ctx.campaign_id} in knowledge base"
                )

        except Exception as e:
            self.logger.error(f"Failed to store experience: {e}", exc_info=True)

    def _cleanup_workspace(self, workspace: str) -> None:
        """清理工作空间

        Args:
            workspace: 工作空间路径
        """
        try:
            workspace_path = Path(workspace)
            if workspace_path.exists() and workspace_path.is_dir():
                # 安全检查: 确保不会删除重要目录
                if workspace_path.name.startswith("campaign_"):
                    shutil.rmtree(workspace)
                    self.logger.info(f"Cleaned up workspace: {workspace}")
                else:
                    self.logger.warning(
                        f"Skipping cleanup of suspicious path: {workspace}"
                    )
        except Exception as e:
            self.logger.warning(f"Failed to cleanup workspace: {e}")

    def __del__(self) -> None:
        """析构函数, 确保资源被清理"""
        # 取消任何未完成的清理任务
        for task in self._cleanup_handlers:
            if not task.done():
                task.cancel()
