"""
Shadow Process Manager - Dynamic shadow process creation and management for FuzzLM-Agent

This module implements the SOTA (State-of-the-art) dynamic shadow process management
as described in docs/workflow.md. It provides real-time shadow process creation,
monitoring, and promotion during Phase 3 production runs.

Key Improvements:
- Enhanced resource management with strict limits and tracking
- Robust state handling with transition validation
- Automatic error recovery and retry mechanisms
- Comprehensive monitoring and metrics collection
- Type safety with TypedDict and proper annotations
"""

import asyncio
import logging
import time
from collections import deque
from collections.abc import Coroutine
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import (
    Any,
    Callable,
    Optional,
)

from fuzzlm_agent.core.statistical_analysis import PerformanceMetrics
from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient
from fuzzlm_agent.orchestrator.constants import (
    CORPUS_SIZE_NORMALIZATION,
    CRASHES_NORMALIZATION,
    DEFAULT_MAX_SHADOWS,
    DEFAULT_PERFORMANCE_WINDOW,
    DEFAULT_PROMOTION_THRESHOLD,
    DEFAULT_RESOURCE_LIMIT,
    DEFAULT_SHADOW_TIMEOUT_HOURS,
    EXECUTION_RATE_NORMALIZATION,
    HEALTH_CHECK_INTERVAL,
    MAX_MEMORY_USAGE_MB,
    MAX_RETRY_ATTEMPTS,
    RESOURCE_CHECK_INTERVAL,
    TIME_WEIGHT_DECAY,
)
from fuzzlm_agent.orchestrator.types import (
    ShadowStatus as ShadowStatusDict,
)
from fuzzlm_agent.orchestrator.types import (
    ShadowStrategy as ShadowStrategyDict,
)

logger = logging.getLogger(__name__)


class ShadowProcessError(Exception):
    """Base exception for shadow process operations"""


class ShadowCreationError(ShadowProcessError):
    """Raised when shadow process creation fails"""


class ShadowPromotionError(ShadowProcessError):
    """Raised when shadow process promotion fails"""


class ResourceLimitError(ShadowProcessError):
    """Raised when resource limits would be exceeded"""


class ShadowProcessState(Enum):
    """Shadow process lifecycle states"""

    CREATED = "created"
    RUNNING = "running"
    EVALUATING = "evaluating"
    PROMOTED = "promoted"
    TERMINATED = "terminated"
    FAILED = "failed"

    def can_transition_to(self, target_state: "ShadowProcessState") -> bool:
        """Check if transition to target state is valid"""
        valid_transitions: dict[ShadowProcessState, set[ShadowProcessState]] = {
            ShadowProcessState.CREATED: {
                ShadowProcessState.RUNNING,
                ShadowProcessState.FAILED,
                ShadowProcessState.TERMINATED,
            },
            ShadowProcessState.RUNNING: {
                ShadowProcessState.EVALUATING,
                ShadowProcessState.PROMOTED,
                ShadowProcessState.TERMINATED,
                ShadowProcessState.FAILED,
            },
            ShadowProcessState.EVALUATING: {
                ShadowProcessState.PROMOTED,
                ShadowProcessState.TERMINATED,
                ShadowProcessState.FAILED,
            },
            ShadowProcessState.PROMOTED: {ShadowProcessState.TERMINATED},
            ShadowProcessState.TERMINATED: set(),
            ShadowProcessState.FAILED: {ShadowProcessState.TERMINATED},
        }
        return target_state in valid_transitions.get(self, set())


@dataclass
class ShadowProcessEvent:
    """Base class for shadow process events"""

    timestamp: float = field(default_factory=time.time)
    event_type: str = "base_event"


@dataclass
class CreateShadowEvent(ShadowProcessEvent):
    """Event to trigger shadow process creation"""

    event_type: str = "create_shadow"
    reason: str = ""
    new_strategy_code: str = ""
    confidence_score: float = 0.0


@dataclass
class PromoteShadowEvent(ShadowProcessEvent):
    """Event to trigger shadow process promotion"""

    event_type: str = "promote_shadow"
    shadow_id: str = ""
    performance_gain: float = 0.0


@dataclass
class TerminateShadowEvent(ShadowProcessEvent):
    """Event to terminate a shadow process"""

    event_type: str = "terminate_shadow"
    shadow_id: str = ""
    reason: str = ""


@dataclass
class ShadowProcess:
    """Shadow process information and state

    Tracks the complete lifecycle of a shadow process including
    performance history, resource usage, and state transitions.
    """

    id: str
    state: ShadowProcessState = ShadowProcessState.CREATED
    created_at: float = field(default_factory=time.time)
    strategy_code: str = ""
    performance_history: deque[PerformanceMetrics] = field(
        default_factory=lambda: deque(maxlen=10)
    )
    promotion_score: float = 0.0
    resource_limit: float = DEFAULT_RESOURCE_LIMIT

    # Resource tracking
    peak_memory_mb: float = 0.0
    peak_cpu_percent: float = 0.0
    last_health_check: Optional[float] = None
    health_check_failures: int = 0

    # State transition tracking
    state_history: list[tuple[ShadowProcessState, float]] = field(default_factory=list)
    termination_reason: Optional[str] = None

    def __post_init__(self) -> None:
        """Initialize state history"""
        self.state_history.append((self.state, self.created_at))

    def transition_to(self, new_state: ShadowProcessState) -> None:
        """Transition to a new state with validation

        Args:
            new_state: Target state

        Raises:
            ValueError: If transition is invalid
        """
        if not self.state.can_transition_to(new_state):
            raise ValueError(
                f"Invalid state transition: {self.state.value} -> {new_state.value}"
            )

        self.state = new_state
        self.state_history.append((new_state, time.time()))
        logger.debug(f"Shadow {self.id} transitioned to {new_state.value}")

    def update_resource_usage(self, memory_mb: float, cpu_percent: float) -> None:
        """Update resource usage tracking

        Args:
            memory_mb: Current memory usage in MB
            cpu_percent: Current CPU usage percentage
        """
        self.peak_memory_mb = max(self.peak_memory_mb, memory_mb)
        self.peak_cpu_percent = max(self.peak_cpu_percent, cpu_percent)
        self.last_health_check = time.time()

    def is_healthy(self, max_failures: int = 3) -> bool:
        """Check if shadow process is healthy

        Args:
            max_failures: Maximum allowed health check failures

        Returns:
            bool: True if healthy
        """
        if self.state in {ShadowProcessState.TERMINATED, ShadowProcessState.FAILED}:
            return False

        if self.health_check_failures >= max_failures:
            return False

        # Check if health check is stale
        if self.last_health_check:
            time_since_check = time.time() - self.last_health_check
            if time_since_check > HEALTH_CHECK_INTERVAL * 2:
                return False

        return True

    def get_average_performance(self) -> dict[str, float]:
        """Calculate average performance metrics

        Returns:
            Dict[str, float]: Average metrics
        """
        if not self.performance_history:
            return {
                "coverage": 0.0,
                "crashes_found": 0.0,
                "exec_per_sec": 0.0,
                "corpus_size": 0.0,
            }

        totals = {
            "coverage": 0.0,
            "crashes_found": 0.0,
            "exec_per_sec": 0.0,
            "corpus_size": 0.0,
        }

        for metrics in self.performance_history:
            totals["coverage"] += metrics.coverage_percentage
            totals["crashes_found"] += metrics.crashes_found
            totals["exec_per_sec"] += metrics.execution_rate
            totals["corpus_size"] += metrics.corpus_size

        count = len(self.performance_history)
        return {k: v / count for k, v in totals.items()}


class ShadowProcessManager:
    """
    Manages shadow process lifecycle during Phase 3 production runs.

    Key responsibilities:
    1. Monitor events for shadow process creation triggers
    2. Create and manage shadow processes with resource constraints
    3. Monitor shadow process performance in real-time
    4. Make promotion decisions based on performance comparison
    5. Handle shadow process termination and cleanup
    """

    def __init__(
        self,
        runtime_client: RuntimeClient,
        telemetry_collector: Any,  # Can be TelemetryReader or similar
        config: dict[str, Any],
        champion_id: Optional[str] = None,  # Add champion_id parameter
    ):
        self.runtime_client = runtime_client
        self.telemetry = telemetry_collector
        self.config = config
        self.champion_id = champion_id  # Store champion_id

        # Event queue for inter-component communication
        self.event_queue: asyncio.Queue[ShadowProcessEvent] = asyncio.Queue()

        # Active shadow processes
        self.shadow_processes: dict[str, ShadowProcess] = {}

        # Champion performance tracking
        self.champion_metrics: deque[PerformanceMetrics] = deque(maxlen=10)

        # Configuration
        self.max_shadows = config.get("shadow", {}).get(
            "max_concurrent", DEFAULT_MAX_SHADOWS
        )
        self.performance_window = config.get("shadow", {}).get(
            "performance_window", DEFAULT_PERFORMANCE_WINDOW
        )
        self.promotion_threshold = config.get("shadow", {}).get(
            "promotion_threshold", DEFAULT_PROMOTION_THRESHOLD
        )
        self.shadow_timeout = config.get("shadow", {}).get(
            "timeout_hours", DEFAULT_SHADOW_TIMEOUT_HOURS
        )
        self.resource_limit = config.get("shadow", {}).get(
            "resource_limit", DEFAULT_RESOURCE_LIMIT
        )

        # State
        self.running = False
        self._background_tasks: list[asyncio.Task[Any]] = []
        self._heartbeat_callback: Optional[Callable[[str], None]] = None

        # Resource tracking
        self._total_resource_usage = {"memory_mb": 0.0, "cpu_percent": 0.0}
        self._resource_lock = asyncio.Lock()

    async def start(self) -> None:
        """Start the shadow process manager"""
        self.running = True
        logger.info("Shadow Process Manager started")

        # Start background tasks
        tasks = [
            self._create_task("event_processor", self._event_processor),
            self._create_task("performance_monitor", self._performance_monitor),
            self._create_task("lifecycle_manager", self._lifecycle_manager),
            self._create_task("resource_monitor", self._resource_monitor),
        ]

        self._background_tasks = tasks

        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            logger.info("Shadow Process Manager tasks cancelled")
            raise
        except Exception as e:
            logger.error(f"Shadow Process Manager error: {e}")
            raise

    def _create_task(
        self, name: str, coro_func: Callable[[], Coroutine[Any, Any, Any]]
    ) -> asyncio.Task[Any]:
        """Create a named task for easier debugging

        Args:
            name: Task name
            coro_func: Coroutine function

        Returns:
            asyncio.Task: Created task
        """
        task: asyncio.Task[Any] = asyncio.create_task(
            coro_func(), name=f"shadow_{name}"
        )
        return task

    async def stop(self) -> None:
        """Stop the shadow process manager gracefully"""
        logger.info("Stopping Shadow Process Manager...")
        self.running = False

        # Terminate all shadow processes
        termination_tasks = []
        for shadow_id in list(self.shadow_processes.keys()):
            termination_tasks.append(
                self._terminate_shadow(shadow_id, "Manager shutdown")
            )

        if termination_tasks:
            await asyncio.gather(*termination_tasks, return_exceptions=True)

        # Cancel background tasks
        for task in self._background_tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete
        if self._background_tasks:
            await asyncio.gather(*self._background_tasks, return_exceptions=True)

        self._background_tasks.clear()
        logger.info("Shadow Process Manager stopped")

    async def request_shadow(
        self, reason: str, new_strategy_code: str, confidence: float
    ) -> None:
        """Request creation of a new shadow process"""
        event = CreateShadowEvent(
            reason=reason,
            new_strategy_code=new_strategy_code,
            confidence_score=confidence,
        )
        await self.event_queue.put(event)
        logger.info(
            f"Shadow process requested: {reason} (confidence: {confidence:.2f})"
        )

    async def _event_processor(self) -> None:
        """Process events from the event queue"""
        last_heartbeat = time.time()

        while self.running:
            try:
                # Update heartbeat periodically
                current_time = time.time()
                if current_time - last_heartbeat > 30:  # Update every 30 seconds
                    if self._heartbeat_callback:
                        self._heartbeat_callback("shadow_manager")
                    last_heartbeat = current_time
                    logger.debug("Shadow manager heartbeat updated")

                # Wait for event with timeout
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)

                if isinstance(event, CreateShadowEvent):
                    await self._handle_create_shadow(event)
                elif isinstance(event, PromoteShadowEvent):
                    await self._handle_promote_shadow(event)
                elif isinstance(event, TerminateShadowEvent):
                    await self._handle_terminate_shadow(event)

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error processing event: {e}")

    async def _handle_create_shadow(self, event: CreateShadowEvent) -> None:
        """Handle shadow process creation request with retry logic

        Args:
            event: Shadow creation event
        """
        # Check resource limits
        if not await self._can_create_shadow():
            logger.warning("Cannot create shadow: resource limits exceeded")
            return

        # Retry logic for shadow creation
        last_error: Optional[Exception] = None

        for attempt in range(MAX_RETRY_ATTEMPTS):
            try:
                # Create shadow process via gRPC
                champion_id = self.champion_id
                if not champion_id:
                    logger.error("No champion_id available for shadow creation")
                    return

                # Create shadow strategy with resource limits
                shadow_strategy: ShadowStrategyDict = {
                    "name": f"shadow_{int(time.time())}",
                    "custom_code": event.new_strategy_code,
                    "resource_limit": self.resource_limit,
                    "mutators": [],  # Will be inherited from champion
                    "scheduler": {},  # Will be inherited from champion
                    "feedback": {},  # Will be inherited from champion
                }

                shadow_id = await self.runtime_client.spawn_shadow(
                    champion_id=champion_id, shadow_strategy=dict(shadow_strategy)
                )

                if shadow_id:
                    shadow = ShadowProcess(
                        id=shadow_id,
                        state=ShadowProcessState.CREATED,
                        strategy_code=event.new_strategy_code,
                        resource_limit=self.resource_limit,
                    )

                    # Transition to running state
                    shadow.transition_to(ShadowProcessState.RUNNING)

                    self.shadow_processes[shadow_id] = shadow

                    # Update resource tracking
                    await self._update_resource_tracking()

                    logger.info(
                        f"Shadow process created: {shadow.id} "
                        f"(attempt {attempt + 1}/{MAX_RETRY_ATTEMPTS})"
                    )
                    return
                else:
                    raise ShadowCreationError("No ID returned from runtime")

            except Exception as e:
                last_error = e
                logger.error(
                    f"Shadow creation attempt {attempt + 1}/{MAX_RETRY_ATTEMPTS} failed: {e}"
                )

                if attempt < MAX_RETRY_ATTEMPTS - 1:
                    # Exponential backoff
                    await asyncio.sleep(2**attempt)

        # All retries failed
        logger.error(
            f"Failed to create shadow after {MAX_RETRY_ATTEMPTS} attempts: {last_error}"
        )

        # Create a failed shadow entry for tracking
        if event.new_strategy_code:
            failed_shadow = ShadowProcess(
                id=f"failed_{int(time.time())}",
                state=ShadowProcessState.FAILED,
                strategy_code=event.new_strategy_code,
                termination_reason=f"Creation failed: {last_error}",
            )
            self.shadow_processes[failed_shadow.id] = failed_shadow

    async def _handle_promote_shadow(self, event: PromoteShadowEvent) -> None:
        """Handle shadow process promotion"""
        shadow = self.shadow_processes.get(event.shadow_id)
        if not shadow:
            logger.error(f"Shadow process not found: {event.shadow_id}")
            return

        try:
            # Promote shadow to champion
            success = await self.runtime_client.promote_shadow(event.shadow_id)

            if success:
                shadow.state = ShadowProcessState.PROMOTED
                logger.info(
                    f"Shadow process promoted: {event.shadow_id} (gain: {event.performance_gain:.2%})"
                )

                # Clean up promoted shadow
                del self.shadow_processes[event.shadow_id]
            else:
                logger.error(f"Failed to promote shadow: {event.shadow_id}")

        except Exception as e:
            logger.error(f"Error promoting shadow process: {e}")

    async def _handle_terminate_shadow(self, event: TerminateShadowEvent) -> None:
        """Handle shadow process termination"""
        await self._terminate_shadow(event.shadow_id, event.reason)

    async def _terminate_shadow(self, shadow_id: str, reason: str) -> None:
        """Terminate a shadow process with proper cleanup

        Args:
            shadow_id: Shadow process ID
            reason: Termination reason
        """
        shadow = self.shadow_processes.get(shadow_id)
        if not shadow:
            return

        try:
            # Update state
            if shadow.state not in {
                ShadowProcessState.TERMINATED,
                ShadowProcessState.FAILED,
            }:
                shadow.transition_to(ShadowProcessState.TERMINATED)
                shadow.termination_reason = reason

            # Stop the shadow process
            await self.runtime_client.stop_fuzzer(shadow_id)

            # Update resource tracking
            await self._update_resource_tracking()

            logger.info(f"Shadow process terminated: {shadow_id} ({reason})")

        except Exception as e:
            logger.error(f"Error terminating shadow process: {e}")
            if shadow.state != ShadowProcessState.FAILED:
                shadow.transition_to(ShadowProcessState.FAILED)
                shadow.termination_reason = f"Termination failed: {e}"

        finally:
            # Remove from active shadows after a delay
            await asyncio.sleep(1.0)  # Allow final metrics collection
            if shadow_id in self.shadow_processes:
                del self.shadow_processes[shadow_id]

    async def _performance_monitor(self) -> None:
        """Monitor performance of champion and shadow processes"""
        last_heartbeat = time.time()

        while self.running:
            try:
                # Update heartbeat periodically
                current_time = time.time()
                if current_time - last_heartbeat > 30:  # Update every 30 seconds
                    if self._heartbeat_callback:
                        self._heartbeat_callback("shadow_manager")
                    last_heartbeat = current_time
                    logger.debug("Shadow performance monitor heartbeat updated")

                # Collect champion metrics
                if self.champion_id:
                    champion_stats = await self._collect_performance_metrics(
                        self.champion_id
                    )
                    if champion_stats:
                        self.champion_metrics.append(champion_stats)
                        logger.debug(
                            f"Champion metrics collected: exec_rate={champion_stats.execution_rate:.1f}/s"
                        )

                # Collect shadow metrics and evaluate
                active_shadows = 0
                for shadow_id, shadow in list(self.shadow_processes.items()):
                    if shadow.state != ShadowProcessState.RUNNING:
                        continue

                    active_shadows += 1
                    shadow_stats = await self._collect_performance_metrics(shadow_id)
                    if shadow_stats:
                        shadow.performance_history.append(shadow_stats)
                        logger.debug(
                            f"Shadow {shadow_id} metrics: exec_rate={shadow_stats.execution_rate:.1f}/s, "
                            f"coverage={shadow_stats.coverage_percentage:.2f}%"
                        )

                        # Evaluate promotion
                        if len(shadow.performance_history) >= 3:
                            await self._evaluate_shadow_promotion(shadow_id)

                if active_shadows > 0:
                    logger.info(
                        f"Performance monitor: tracking {active_shadows} active shadow(s)"
                    )

                # Wait before next collection
                await asyncio.sleep(30)  # Collect every 30 seconds

            except Exception as e:
                logger.error(f"Error in performance monitor: {e}")
                await asyncio.sleep(30)

    async def _collect_performance_metrics(
        self, fuzzer_id: str
    ) -> Optional[PerformanceMetrics]:
        """Collect performance metrics for a specific fuzzer"""
        try:
            # Get latest telemetry data
            # Use read_batch method which is available in TelemetryReader
            if hasattr(self.telemetry, "read_batch"):
                latest_data = await self.telemetry.read_batch(max_entries=1000)
            elif hasattr(self.telemetry, "get_latest_data"):
                latest_data = self.telemetry.get_latest_data()
            else:
                logger.warning("Telemetry collector doesn't have expected interface")
                return None

            # Extract metrics for the specific fuzzer
            metrics = PerformanceMetrics()

            # Find relevant telemetry entries
            for entry in latest_data:
                if entry.get("fuzzer_id") == fuzzer_id or (
                    fuzzer_id == self.champion_id and not entry.get("fuzzer_id")
                ):
                    entry_type = entry.get("type")
                    data = entry.get("data", {})

                    if entry_type == "ExecutionStats":
                        metrics.execution_rate = data.get("exec_per_sec", 0)
                    elif entry_type == "CoverageUpdate":
                        metrics.coverage_percentage = data.get("total_coverage", 0)
                    elif entry_type == "CrashFound":
                        metrics.crashes_found += 1
                    elif entry_type == "CorpusUpdate":
                        metrics.corpus_size = data.get("corpus_size", 0)

            return metrics

        except Exception as e:
            logger.error(f"Error collecting metrics for {fuzzer_id}: {e}")
            return None

    async def _evaluate_shadow_promotion(self, shadow_id: str) -> None:
        """Evaluate if a shadow process should be promoted"""
        shadow = self.shadow_processes.get(shadow_id)
        if not shadow or shadow.state != ShadowProcessState.RUNNING:
            return

        # Calculate performance score
        shadow_score = self._calculate_performance_score(shadow.performance_history)
        champion_score = self._calculate_performance_score(self.champion_metrics)

        if champion_score > 0:
            performance_gain = (shadow_score - champion_score) / champion_score
            shadow.promotion_score = performance_gain

            # Check if shadow consistently outperforms champion
            if performance_gain > self.promotion_threshold:
                logger.info(
                    f"Shadow {shadow_id} outperforms champion by {performance_gain:.2%}"
                )

                # Create promotion event
                event = PromoteShadowEvent(
                    shadow_id=shadow_id, performance_gain=performance_gain
                )
                await self.event_queue.put(event)

    def _calculate_performance_score(self, metrics: deque[PerformanceMetrics]) -> float:
        """Calculate weighted performance score from metrics history"""
        if not metrics:
            return 0.0

        # Weights for different metrics
        weights = {"coverage": 0.4, "crashes": 0.3, "exec_speed": 0.2, "corpus": 0.1}

        # Calculate weighted average with time decay
        total_score = 0.0
        total_weight = 0.0

        for i, metric in enumerate(metrics):
            # Time decay factor (more recent = higher weight)
            time_weight = 1.0 - (TIME_WEIGHT_DECAY * (len(metrics) - 1 - i))

            # Calculate metric score
            score = (
                metric.coverage_percentage * weights["coverage"]
                + min(metric.crashes_found / CRASHES_NORMALIZATION, 1.0)
                * weights["crashes"]  # Normalize crashes
                + min(metric.execution_rate / EXECUTION_RATE_NORMALIZATION, 1.0)
                * weights["exec_speed"]  # Normalize exec/s
                + min(metric.corpus_size / CORPUS_SIZE_NORMALIZATION, 1.0)
                * weights["corpus"]  # Normalize corpus
            )

            total_score += score * time_weight
            total_weight += time_weight

        return total_score / total_weight if total_weight > 0 else 0.0

    async def _lifecycle_manager(self) -> None:
        """Manage shadow process lifecycle (timeouts, resource limits, etc.)"""
        while self.running:
            try:
                current_time = time.time()

                for shadow_id, shadow in list(self.shadow_processes.items()):
                    # Check timeout
                    if current_time - shadow.created_at > self.shadow_timeout * 3600:
                        logger.info(f"Shadow {shadow_id} timed out")
                        await self._terminate_shadow(shadow_id, "Timeout")
                        continue

                    # Check if shadow is underperforming
                    if (
                        len(shadow.performance_history) >= 5
                        and shadow.promotion_score < -0.1
                    ):
                        logger.info(f"Shadow {shadow_id} underperforming")
                        await self._terminate_shadow(shadow_id, "Underperforming")

                # Wait before next check
                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error in lifecycle manager: {e}")
                await asyncio.sleep(60)

    async def _can_create_shadow(self) -> bool:
        """Check if we can create a new shadow process

        Returns:
            bool: True if shadow creation is allowed
        """
        # Check count limit
        active_shadows = sum(
            1
            for s in self.shadow_processes.values()
            if s.state in {ShadowProcessState.RUNNING, ShadowProcessState.EVALUATING}
        )

        if active_shadows >= self.max_shadows:
            logger.warning(f"Maximum active shadows ({self.max_shadows}) reached")
            return False

        # Check resource limits
        async with self._resource_lock:
            total_cpu = self._total_resource_usage["cpu_percent"]
            total_memory = self._total_resource_usage["memory_mb"]

            # Estimate resource usage for new shadow
            estimated_cpu = total_cpu + (self.resource_limit * 100)
            estimated_memory = total_memory + MAX_MEMORY_USAGE_MB

            # Check system limits (leave 20% headroom)
            if estimated_cpu > 80.0:
                logger.warning(
                    f"CPU resource limit would be exceeded: {estimated_cpu:.1f}%"
                )
                return False

            if estimated_memory > (MAX_MEMORY_USAGE_MB * self.max_shadows * 1.2):
                logger.warning(
                    f"Memory resource limit would be exceeded: {estimated_memory:.1f}MB"
                )
                return False

        return True

    async def _update_resource_tracking(self) -> None:
        """Update total resource usage tracking"""
        async with self._resource_lock:
            total_cpu = 0.0
            total_memory = 0.0

            for shadow in self.shadow_processes.values():
                if shadow.state == ShadowProcessState.RUNNING:
                    # Use peak values as estimates
                    total_cpu += shadow.peak_cpu_percent
                    total_memory += shadow.peak_memory_mb

            self._total_resource_usage["cpu_percent"] = total_cpu
            self._total_resource_usage["memory_mb"] = total_memory

            logger.debug(
                f"Resource usage - CPU: {total_cpu:.1f}%, Memory: {total_memory:.1f}MB"
            )

    async def _resource_monitor(self) -> None:
        """Monitor resource usage of shadow processes"""
        while self.running:
            try:
                # Collect resource metrics for each shadow
                for shadow_id, shadow in list(self.shadow_processes.items()):
                    if shadow.state != ShadowProcessState.RUNNING:
                        continue

                    # Get resource usage from telemetry or system
                    resource_data = await self._get_process_resources(shadow_id)
                    if resource_data:
                        shadow.update_resource_usage(
                            resource_data.get("memory_mb", 0.0),
                            resource_data.get("cpu_percent", 0.0),
                        )

                        # Check resource limits
                        if (
                            resource_data.get("cpu_percent", 0)
                            > self.resource_limit * 100
                        ):
                            logger.warning(
                                f"Shadow {shadow_id} exceeding CPU limit: "
                                f"{resource_data['cpu_percent']:.1f}%"
                            )

                        if resource_data.get("memory_mb", 0) > MAX_MEMORY_USAGE_MB:
                            logger.warning(
                                f"Shadow {shadow_id} exceeding memory limit: "
                                f"{resource_data['memory_mb']:.1f}MB"
                            )
                            # Consider terminating if consistently over limit
                            shadow.health_check_failures += 1
                    else:
                        # Failed to get resources
                        shadow.health_check_failures += 1

                # Update total tracking
                await self._update_resource_tracking()

                # Check for unhealthy shadows
                for shadow_id, shadow in list(self.shadow_processes.items()):
                    if not shadow.is_healthy():
                        logger.warning(f"Shadow {shadow_id} is unhealthy")
                        await self._terminate_shadow(shadow_id, "Health check failed")

                # Wait before next check
                await asyncio.sleep(RESOURCE_CHECK_INTERVAL)

            except Exception as e:
                logger.error(f"Error in resource monitor: {e}")
                await asyncio.sleep(RESOURCE_CHECK_INTERVAL)

    async def _get_process_resources(
        self, shadow_id: str
    ) -> Optional[dict[str, float]]:
        """Get resource usage for a specific process

        Args:
            shadow_id: Shadow process ID

        Returns:
            Optional[Dict[str, float]]: Resource usage data or None
        """
        try:
            # Try to get from telemetry first
            if hasattr(self.telemetry, "get_process_resources"):
                result = await self.telemetry.get_process_resources(shadow_id)
                # Ensure the result is properly typed
                if isinstance(result, dict):
                    return result
                return None

            # Fallback: estimate based on telemetry data
            # This is a simplified estimation
            return {
                "memory_mb": 100.0,  # Default estimate
                "cpu_percent": self.resource_limit * 100 * 0.8,  # 80% of limit
            }

        except Exception as e:
            logger.error(f"Failed to get resources for {shadow_id}: {e}")
            return None

    def get_status(self) -> dict[str, Any]:
        """Get current status of shadow processes

        Returns:
            Dict[str, Any]: Comprehensive status information
        """
        shadows_info: dict[str, ShadowStatusDict] = {}

        for shadow_id, shadow in self.shadow_processes.items():
            shadows_info[shadow_id] = ShadowStatusDict(
                id=shadow_id,
                state=shadow.state.value,
                created_at=datetime.fromtimestamp(shadow.created_at).isoformat(),
                promotion_score=shadow.promotion_score,
                performance_history_length=len(shadow.performance_history),
                resource_usage={
                    "peak_memory_mb": shadow.peak_memory_mb,
                    "peak_cpu_percent": shadow.peak_cpu_percent,
                },
                last_health_check=(
                    datetime.fromtimestamp(shadow.last_health_check).isoformat()
                    if shadow.last_health_check
                    else None
                ),
            )

        return {
            "active_shadows": sum(
                1
                for s in self.shadow_processes.values()
                if s.state == ShadowProcessState.RUNNING
            ),
            "total_shadows": len(self.shadow_processes),
            "resource_usage": dict(self._total_resource_usage),
            "config": {
                "max_shadows": self.max_shadows,
                "promotion_threshold": self.promotion_threshold,
                "resource_limit": self.resource_limit,
            },
            "shadows": shadows_info,
        }
